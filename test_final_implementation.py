#!/usr/bin/env python3
"""
Test the final implementation with improved user experience
"""

import sys
from pathlib import Path

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent))

def test_camera_configurations():
    """Test different camera configurations"""
    print("🧪 Testing Camera Configurations")
    print("=" * 50)
    
    from ultralytics.solutions.streamlit_inference import Inference
    
    configs = [
        ("Reliable Dual Camera", [0, 2]),
        ("Single Camera 0", [0]),
        ("Single Camera 2", [2]),
        ("Single Camera 4", [4]),
        ("Single Camera 6", [6]),
        ("All Cameras (may fail)", [0, 2, 4, 6]),
    ]
    
    for config_name, camera_indices in configs:
        print(f"\n📷 Testing {config_name}: {camera_indices}")
        
        try:
            inf = Inference()
            inf.camera_indices = camera_indices
            inf.num_cameras = len(camera_indices)
            inf.frame_width = 640  # Use lower resolution for testing
            inf.frame_height = 480
            
            # Test camera setup
            success = inf.setup_cameras()
            working_cameras = sum(1 for cap in inf.camera_caps if cap is not None)
            
            if success:
                print(f"  ✅ Success: {working_cameras}/{len(camera_indices)} cameras working")
            else:
                print(f"  ❌ Failed: No cameras working")
            
            # Cleanup
            for cap in inf.camera_caps:
                if cap is not None:
                    cap.release()
                    
        except Exception as e:
            print(f"  ❌ Exception: {e}")

def test_resolution_settings():
    """Test resolution setting functionality"""
    print(f"\n🔧 Testing Resolution Settings")
    print("-" * 50)
    
    from ultralytics.solutions.streamlit_inference import Inference
    
    resolutions = [
        (640, 480),
        (1280, 720),
        (1920, 1080),
    ]
    
    for width, height in resolutions:
        print(f"\n📐 Testing {width}x{height}")
        
        try:
            inf = Inference()
            inf.camera_indices = [0]  # Use reliable camera
            inf.num_cameras = 1
            inf.frame_width = width
            inf.frame_height = height
            
            success = inf.setup_cameras()
            if success and inf.camera_caps[0] is not None:
                cap = inf.camera_caps[0]
                actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                print(f"  ✅ Set to {actual_width}x{actual_height}")
                cap.release()
            else:
                print(f"  ❌ Failed to set resolution")
                
        except Exception as e:
            print(f"  ❌ Exception: {e}")

def test_streamlit_compatibility():
    """Test Streamlit app compatibility"""
    print(f"\n🌐 Testing Streamlit Compatibility")
    print("-" * 50)
    
    try:
        from ultralytics.solutions.streamlit_inference import Inference
        
        # Test initialization without Streamlit
        inf = Inference()
        print("✅ Inference class initialization: OK")
        
        # Test default configuration
        print(f"📊 Default config: {inf.num_cameras} cameras, indices {inf.camera_indices}")
        print(f"📐 Default resolution: {inf.frame_width}x{inf.frame_height}")
        
        # Test method existence
        methods = ['setup_cameras', 'multi_camera_inference', 'camera_thread', 'sidebar', 'configure']
        for method in methods:
            if hasattr(inf, method):
                print(f"✅ Method {method}: exists")
            else:
                print(f"❌ Method {method}: missing")
        
        return True
        
    except Exception as e:
        print(f"❌ Streamlit compatibility test failed: {e}")
        return False

def generate_usage_guide():
    """Generate a usage guide based on test results"""
    print(f"\n📋 Usage Guide")
    print("=" * 50)
    
    print("""
🚀 **How to Use Your Multi-Camera Setup**

1. **Start the Application**:
   streamlit run ultralytics/solutions/streamlit_inference.py

2. **Recommended Configurations**:
   
   🟢 **Reliable Setup** (Recommended):
   - Source: multi-webcam
   - Camera Configuration: "Reliable (0,2)"
   - Resolution: 1280x720
   - Expected: 2 cameras working reliably
   
   🟡 **Single Camera** (High Quality):
   - Source: multi-webcam  
   - Camera Configuration: "Single Camera 0" (or 2, 4, 6)
   - Resolution: 1920x1080
   - Expected: 1 camera at high resolution
   
   🔴 **All Cameras** (Experimental):
   - Source: multi-webcam
   - Camera Configuration: "All Available (0,2,4,6)"
   - Resolution: 640x480
   - Expected: 2-4 cameras (USB bandwidth dependent)

3. **Troubleshooting**:
   - If cameras fail: Try "Reliable (0,2)" preset
   - If low FPS: Reduce resolution to 640x480
   - If app crashes: Use single camera mode first

4. **Performance Tips**:
   - Use yolo11n.pt for fastest inference
   - Lower resolution = higher FPS
   - 2 cameras = optimal balance
   """)

def main():
    """Main test function"""
    print("🔬 Final Implementation Test")
    print("=" * 60)
    
    # Import cv2 here to avoid issues if not available
    global cv2
    try:
        import cv2
    except ImportError:
        print("❌ OpenCV not available, skipping camera tests")
        cv2 = None
    
    # Test 1: Streamlit compatibility
    streamlit_ok = test_streamlit_compatibility()
    
    if cv2 and streamlit_ok:
        # Test 2: Camera configurations
        test_camera_configurations()
        
        # Test 3: Resolution settings  
        test_resolution_settings()
    
    # Test 4: Generate usage guide
    generate_usage_guide()
    
    print(f"\n✅ Final implementation testing completed!")
    
    if streamlit_ok:
        print("\n🎉 **Your multi-camera system is ready!**")
        print("   Run: streamlit run ultralytics/solutions/streamlit_inference.py")
        print("   Use: 'Reliable (0,2)' configuration for best results")
    else:
        print("\n⚠️  Please check the error messages above")

if __name__ == "__main__":
    main()
