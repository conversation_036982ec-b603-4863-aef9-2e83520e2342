# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license

import io
import os
import threading
import time
from typing import Any, Dict, List, Tuple

import cv2
import numpy as np
import torch

from ultralytics import YOLO
from ultralytics.utils import LOGGER
from ultralytics.utils.checks import check_requirements
from ultralytics.utils.downloads import GITHUB_ASSETS_STEMS

torch.classes.__path__ = []  # Torch module __path__._path issue: https://github.com/datalab-to/marker/issues/442


class Inference:
    """
    A class to perform object detection, image classification, image segmentation and pose estimation inference.

    This class provides functionalities for loading models, configuring settings, uploading video files, and performing
    real-time inference using Streamlit and Ultralytics YOLO models. Enhanced with multi-camera support and custom resolution.

    Attributes:
        st (module): Streamlit module for UI creation.
        temp_dict (dict): Temporary dictionary to store the model path and other configuration.
        model_path (str): Path to the loaded model.
        model (YOLO): The YOLO model instance.
        source (str): Selected video source (webcam, multi-webcam, or video file).
        enable_trk (bool): Enable tracking option.
        conf (float): Confidence threshold for detection.
        iou (float): IoU threshold for non-maximum suppression.
        org_frame (Any): Container for the original frame to be displayed.
        ann_frame (Any): Container for the annotated frame to be displayed.
        vid_file_name (str | int): Name of the uploaded video file or webcam index.
        selected_ind (List[int]): List of selected class indices for detection.
        num_cameras (int): Number of cameras to use for multi-camera mode.
        camera_indices (List[int]): List of camera indices to use.
        frame_width (int): Custom frame width for cameras.
        frame_height (int): Custom frame height for cameras.
        camera_caps (List[cv2.VideoCapture]): List of camera capture objects.
        camera_threads (List[threading.Thread]): List of camera threads.
        camera_frames (Dict[int, np.ndarray]): Dictionary to store latest frames from each camera.
        camera_running (bool): Flag to control camera threads.

    Methods:
        web_ui: Set up the Streamlit web interface with custom HTML elements.
        sidebar: Configure the Streamlit sidebar for model and inference settings.
        source_upload: Handle video file uploads through the Streamlit interface.
        configure: Configure the model and load selected classes for inference.
        setup_cameras: Initialize multiple cameras with custom resolution.
        camera_thread: Thread function to capture frames from a single camera.
        multi_camera_inference: Perform inference on multiple cameras simultaneously.
        inference: Perform real-time object detection inference.

    Examples:
        Create an Inference instance with a custom model and 4 cameras
        >>> inf = Inference(model="path/to/model.pt")
        >>> inf.inference()

        Create an Inference instance with default settings
        >>> inf = Inference()
        >>> inf.inference()
    """

    def __init__(self, **kwargs: Any) -> None:
        """
        Initialize the Inference class, checking Streamlit requirements and setting up the model path.

        Args:
            **kwargs (Any): Additional keyword arguments for model configuration.
        """
        check_requirements("streamlit>=1.29.0")  # scope imports for faster ultralytics package load speeds
        import streamlit as st

        self.st = st  # Reference to the Streamlit module
        self.source = None  # Video source selection (webcam, multi-webcam, or video file)
        self.img_file_names = []  # List of image file names
        self.enable_trk = False  # Flag to toggle object tracking
        self.conf = 0.25  # Confidence threshold for detection
        self.iou = 0.45  # Intersection-over-Union (IoU) threshold for non-maximum suppression
        self.org_frame = None  # Container for the original frame display
        self.ann_frame = None  # Container for the annotated frame display
        self.vid_file_name = None  # Video file name or webcam index
        self.selected_ind: List[int] = []  # List of selected class indices for detection
        self.model = None  # YOLO model instance

        # Multi-camera support attributes
        self.num_cameras = 4  # Default number of cameras
        self.camera_indices = [0, 1, 2, 3]  # Default camera indices
        self.frame_width = 640  # Default frame width
        self.frame_height = 480  # Default frame height
        self.camera_caps: List[cv2.VideoCapture] = []  # Camera capture objects
        self.camera_threads: List[threading.Thread] = []  # Camera threads
        self.camera_frames: Dict[int, np.ndarray] = {}  # Latest frames from each camera
        self.camera_running = False  # Flag to control camera threads
        self.camera_containers: Dict[int, Tuple[Any, Any]] = {}  # UI containers for each camera

        self.temp_dict = {"model": None, **kwargs}
        self.model_path = None  # Model file path
        if self.temp_dict["model"] is not None:
            self.model_path = self.temp_dict["model"]

        LOGGER.info(f"Ultralytics Solutions: ✅ {self.temp_dict}")

    def web_ui(self) -> None:
        """Set up the Streamlit web interface with custom HTML elements."""
        menu_style_cfg = """<style>MainMenu {visibility: hidden;}</style>"""  # Hide main menu style

        # Main title of streamlit application
        main_title_cfg = """<div><h1 style="color:#111F68; text-align:center; font-size:40px; margin-top:-50px;
        font-family: 'Archivo', sans-serif; margin-bottom:20px;">Ultralytics YOLO Streamlit Application</h1></div>"""

        # Subtitle of streamlit application
        sub_title_cfg = """<div><h5 style="color:#042AFF; text-align:center; font-family: 'Archivo', sans-serif; 
        margin-top:-15px; margin-bottom:50px;">Experience real-time object detection on your webcam, videos, and images 
        with the power of Ultralytics YOLO! 🚀</h5></div>"""

        # Set html page configuration and append custom HTML
        self.st.set_page_config(page_title="Ultralytics Streamlit App", layout="wide")
        self.st.markdown(menu_style_cfg, unsafe_allow_html=True)
        self.st.markdown(main_title_cfg, unsafe_allow_html=True)
        self.st.markdown(sub_title_cfg, unsafe_allow_html=True)

    def sidebar(self) -> None:
        """Configure the Streamlit sidebar for model and inference settings."""
        with self.st.sidebar:  # Add Ultralytics LOGO
            logo = "https://raw.githubusercontent.com/ultralytics/assets/main/logo/Ultralytics_Logotype_Original.svg"
            self.st.image(logo, width=250)

        self.st.sidebar.title("User Configuration")  # Add elements to vertical setting menu
        self.source = self.st.sidebar.selectbox(
            "Source",
            ("webcam", "multi-webcam", "video", "image"),
        )  # Add source selection dropdown

        # Camera configuration
        if self.source in ["webcam", "multi-webcam"]:
            self.st.sidebar.subheader("Camera Settings")

            if self.source == "multi-webcam":
                self.num_cameras = self.st.sidebar.slider("Number of Cameras", 1, 8, self.num_cameras)

                # Camera indices selection
                camera_indices_str = self.st.sidebar.text_input(
                    "Camera Indices (comma-separated)",
                    ",".join(map(str, self.camera_indices[:self.num_cameras]))
                )
                try:
                    self.camera_indices = [int(x.strip()) for x in camera_indices_str.split(",")][:self.num_cameras]
                except ValueError:
                    self.st.sidebar.error("Invalid camera indices format. Using default values.")
                    self.camera_indices = list(range(self.num_cameras))

            # Resolution settings for both single and multi-camera
            resolution_presets = {
                "640x480": (640, 480),
                "1280x720": (1280, 720),
                "1920x1080": (1920, 1080),
                "Custom": None
            }
            selected_resolution = self.st.sidebar.selectbox("Resolution", list(resolution_presets.keys()))

            if selected_resolution == "Custom":
                self.frame_width = self.st.sidebar.number_input("Width", min_value=320, max_value=3840, value=self.frame_width)
                self.frame_height = self.st.sidebar.number_input("Height", min_value=240, max_value=2160, value=self.frame_height)
            else:
                self.frame_width, self.frame_height = resolution_presets[selected_resolution]

        if self.source in ["webcam", "multi-webcam", "video"]:
            self.enable_trk = self.st.sidebar.radio("Enable Tracking", ("Yes", "No")) == "Yes"  # Enable object tracking
        self.conf = float(
            self.st.sidebar.slider("Confidence Threshold", 0.0, 1.0, self.conf, 0.01)
        )  # Slider for confidence
        self.iou = float(self.st.sidebar.slider("IoU Threshold", 0.0, 1.0, self.iou, 0.01))  # Slider for NMS threshold

        # Create UI layout based on source type
        if self.source == "multi-webcam":
            # Create grid layout for multiple cameras
            cols_per_row = 2 if self.num_cameras <= 4 else 3
            rows = (self.num_cameras + cols_per_row - 1) // cols_per_row

            for row in range(rows):
                cols = self.st.columns(cols_per_row)
                for col_idx in range(cols_per_row):
                    cam_idx = row * cols_per_row + col_idx
                    if cam_idx < self.num_cameras:
                        with cols[col_idx]:
                            self.st.markdown(f"**Camera {self.camera_indices[cam_idx]}**")
                            org_container = self.st.empty()
                            ann_container = self.st.empty()
                            self.camera_containers[cam_idx] = (org_container, ann_container)
        elif self.source != "image":  # Single camera or video
            col1, col2 = self.st.columns(2)  # Create two columns for displaying frames
            self.org_frame = col1.empty()  # Container for original frame
            self.ann_frame = col2.empty()  # Container for annotated frame

    def source_upload(self) -> None:
        """Handle video file uploads through the Streamlit interface."""
        from ultralytics.data.utils import IMG_FORMATS, VID_FORMATS  # scope import

        self.vid_file_name = ""
        if self.source == "video":
            vid_file = self.st.sidebar.file_uploader("Upload Video File", type=VID_FORMATS)
            if vid_file is not None:
                g = io.BytesIO(vid_file.read())  # BytesIO Object
                with open("ultralytics.mp4", "wb") as out:  # Open temporary file as bytes
                    out.write(g.read())  # Read bytes into file
                self.vid_file_name = "ultralytics.mp4"
        elif self.source == "webcam":
            self.vid_file_name = 0  # Use webcam index 0
        elif self.source == "multi-webcam":
            # Multi-webcam mode doesn't use vid_file_name
            pass
        elif self.source == "image":
            import tempfile  # scope import

            imgfiles = self.st.sidebar.file_uploader("Upload Image Files", type=IMG_FORMATS, accept_multiple_files=True)
            if imgfiles:
                for imgfile in imgfiles:  # Save each uploaded image to a temporary file
                    with tempfile.NamedTemporaryFile(delete=False, suffix=f".{imgfile.name.split('.')[-1]}") as tf:
                        tf.write(imgfile.read())
                        self.img_file_names.append({"path": tf.name, "name": imgfile.name})

    def configure(self) -> None:
        """Configure the model and load selected classes for inference."""
        # Add dropdown menu for model selection
        M_ORD, T_ORD = ["yolo11n", "yolo11s", "yolo11m", "yolo11l", "yolo11x"], ["", "-seg", "-pose", "-obb", "-cls"]
        available_models = sorted(
            [
                x.replace("yolo", "YOLO")
                for x in GITHUB_ASSETS_STEMS
                if any(x.startswith(b) for b in M_ORD) and "grayscale" not in x
            ],
            key=lambda x: (M_ORD.index(x[:7].lower()), T_ORD.index(x[7:].lower() or "")),
        )
        if self.model_path:  # Insert user provided custom model in available_models
            available_models.insert(0, self.model_path)
        selected_model = self.st.sidebar.selectbox("Model", available_models)

        with self.st.spinner("Model is downloading..."):
            if (
                selected_model.endswith((".pt", ".onnx", ".torchscript", ".mlpackage", ".engine"))
                or "openvino_model" in selected_model
            ):
                model_path = selected_model
            else:
                model_path = f"{selected_model.lower()}.pt"  # Default to .pt if no model provided during function call.
            self.model = YOLO(model_path)  # Load the YOLO model
            class_names = list(self.model.names.values())  # Convert dictionary to list of class names
        self.st.success("Model loaded successfully!")

        # Multiselect box with class names and get indices of selected classes
        selected_classes = self.st.sidebar.multiselect("Classes", class_names, default=class_names[:3])
        self.selected_ind = [class_names.index(option) for option in selected_classes]

        if not isinstance(self.selected_ind, list):  # Ensure selected_options is a list
            self.selected_ind = list(self.selected_ind)

    def setup_cameras(self) -> bool:
        """
        Initialize multiple cameras with custom resolution.

        Returns:
            bool: True if at least one camera was successfully initialized, False otherwise.
        """
        self.camera_caps = []
        self.camera_frames = {}
        successful_cameras = 0

        for i, cam_idx in enumerate(self.camera_indices):
            try:
                cap = cv2.VideoCapture(cam_idx)
                if cap.isOpened():
                    # Set custom resolution
                    cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.frame_width)
                    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.frame_height)

                    # Verify the resolution was set
                    actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

                    self.camera_caps.append(cap)
                    self.camera_frames[i] = np.zeros((actual_height, actual_width, 3), dtype=np.uint8)
                    successful_cameras += 1

                    LOGGER.info(f"Camera {cam_idx} initialized successfully with resolution {actual_width}x{actual_height}")
                else:
                    self.camera_caps.append(None)
                    LOGGER.warning(f"Failed to open camera {cam_idx}")
            except Exception as e:
                self.camera_caps.append(None)
                LOGGER.error(f"Error initializing camera {cam_idx}: {e}")

        return successful_cameras > 0

    def camera_thread(self, camera_index: int, cap: cv2.VideoCapture) -> None:
        """
        Thread function to capture frames from a single camera.

        Args:
            camera_index (int): Index of the camera in the camera list.
            cap (cv2.VideoCapture): OpenCV VideoCapture object for the camera.
        """
        while self.camera_running and cap is not None and cap.isOpened():
            try:
                ret, frame = cap.read()
                if ret:
                    self.camera_frames[camera_index] = frame.copy()
                else:
                    LOGGER.warning(f"Failed to read frame from camera {camera_index}")
                time.sleep(0.01)  # Small delay to prevent excessive CPU usage
            except Exception as e:
                LOGGER.error(f"Error in camera thread {camera_index}: {e}")
                break

    def multi_camera_inference(self) -> None:
        """Perform inference on multiple cameras simultaneously."""
        if not self.setup_cameras():
            self.st.error("Failed to initialize any cameras. Please check your camera connections.")
            return

        # Start camera threads
        self.camera_running = True
        self.camera_threads = []

        for i, cap in enumerate(self.camera_caps):
            if cap is not None:
                thread = threading.Thread(target=self.camera_thread, args=(i, cap))
                thread.daemon = True
                thread.start()
                self.camera_threads.append(thread)

        stop_button = self.st.sidebar.button("Stop")

        try:
            while self.camera_running:
                if stop_button:
                    break

                # Process frames from all cameras
                for cam_idx in range(len(self.camera_caps)):
                    if cam_idx in self.camera_frames and cam_idx in self.camera_containers:
                        frame = self.camera_frames[cam_idx].copy()

                        if frame is not None and frame.size > 0:
                            # Perform inference
                            if self.enable_trk:
                                results = self.model.track(
                                    frame, conf=self.conf, iou=self.iou, classes=self.selected_ind, persist=True
                                )
                            else:
                                results = self.model(frame, conf=self.conf, iou=self.iou, classes=self.selected_ind)

                            annotated_frame = results[0].plot()

                            # Update UI containers
                            org_container, ann_container = self.camera_containers[cam_idx]
                            org_container.image(frame, channels="BGR", caption=f"Camera {self.camera_indices[cam_idx]} - Original")
                            ann_container.image(annotated_frame, channels="BGR", caption=f"Camera {self.camera_indices[cam_idx]} - Predicted")

                time.sleep(0.03)  # ~30 FPS

        finally:
            # Cleanup
            self.camera_running = False

            # Wait for threads to finish
            for thread in self.camera_threads:
                if thread.is_alive():
                    thread.join(timeout=1.0)

            # Release cameras
            for cap in self.camera_caps:
                if cap is not None:
                    cap.release()

            cv2.destroyAllWindows()

    def image_inference(self) -> None:
        """Perform inference on uploaded images."""
        for idx, img_info in enumerate(self.img_file_names):
            img_path = img_info["path"]
            image = cv2.imread(img_path)  # Load and display the original image
            if image is not None:
                self.st.markdown(f"#### Processed: {img_info['name']}")
                col1, col2 = self.st.columns(2)
                with col1:
                    self.st.image(image, channels="BGR", caption="Original Image")
                results = self.model(image, conf=self.conf, iou=self.iou, classes=self.selected_ind)
                annotated_image = results[0].plot()
                with col2:
                    self.st.image(annotated_image, channels="BGR", caption="Predicted Image")
                try:  # Clean up temporary file
                    os.unlink(img_path)
                except FileNotFoundError:
                    pass  # File doesn't exist, ignore
            else:
                self.st.error("Could not load the uploaded image.")

    def inference(self) -> None:
        """Perform real-time object detection inference on video or webcam feed."""
        self.web_ui()  # Initialize the web interface
        self.sidebar()  # Create the sidebar
        self.source_upload()  # Upload the video source
        self.configure()  # Configure the app

        if self.st.sidebar.button("Start"):
            if self.source == "image":
                if self.img_file_names:
                    self.image_inference()
                else:
                    self.st.info("Please upload an image file to perform inference.")
                return
            elif self.source == "multi-webcam":
                self.multi_camera_inference()
                return

            # Single camera or video processing
            stop_button = self.st.sidebar.button("Stop")  # Button to stop the inference
            cap = cv2.VideoCapture(self.vid_file_name)  # Capture the video

            # Set custom resolution for single webcam
            if self.source == "webcam":
                cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.frame_width)
                cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.frame_height)
                actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                self.st.info(f"Camera resolution set to: {actual_width}x{actual_height}")

            if not cap.isOpened():
                self.st.error("Could not open webcam or video source.")
                return

            while cap.isOpened():
                success, frame = cap.read()
                if not success:
                    self.st.warning("Failed to read frame from webcam. Please verify the webcam is connected properly.")
                    break

                # Process frame with model
                if self.enable_trk:
                    results = self.model.track(
                        frame, conf=self.conf, iou=self.iou, classes=self.selected_ind, persist=True
                    )
                else:
                    results = self.model(frame, conf=self.conf, iou=self.iou, classes=self.selected_ind)

                annotated_frame = results[0].plot()  # Add annotations on frame

                if stop_button:
                    cap.release()  # Release the capture
                    self.st.stop()  # Stop streamlit app

                self.org_frame.image(frame, channels="BGR", caption="Original Frame")  # Display original frame
                self.ann_frame.image(annotated_frame, channels="BGR", caption="Predicted Frame")  # Display processed

            cap.release()  # Release the capture
        cv2.destroyAllWindows()  # Destroy all OpenCV windows


if __name__ == "__main__":
    import sys  # Import the sys module for accessing command-line arguments

    # Check if a model name is provided as a command-line argument
    args = len(sys.argv)
    model = sys.argv[1] if args > 1 else None  # Assign first argument as the model name if provided
    # Create an instance of the Inference class and run inference
    Inference(model=model).inference()
