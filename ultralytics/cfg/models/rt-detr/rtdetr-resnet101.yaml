# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license

# Ultralytics RT-DETR-ResNet101 hybrid object detection model with P3/8 - P5/32 outputs
# Model docs: https://docs.ultralytics.com/models/rtdetr
# Task docs: https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 80 # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov8n-cls.yaml' will call yolov8-cls.yaml with scale 'n'
  # [depth, width, max_channels]
  l: [1.00, 1.00, 1024]

backbone:
  # [from, repeats, module, args]
  - [-1, 1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [3, 64, 1, True, 1]] # 0
  - [-1, 1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [64, 64, 1, False, 3]] # 1
  - [-1, 1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [256, 128, 2, Fals<PERSON>, 4]] # 2
  - [-1, 1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [512, 256, 2, <PERSON><PERSON><PERSON>, 23]] # 3
  - [-1, 1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [1024, 512, 2, <PERSON>alse, 3]] # 4

head:
  - [-1, 1, Conv, [256, 1, 1, None, 1, 1, Fals<PERSON>]] # 5
  - [-1, 1, <PERSON><PERSON>, [1024, 8]]
  - [-1, 1, Conv, [256, 1, 1]] # 7

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [3, 1, Conv, [256, 1, 1, None, 1, 1, False]] # 9
  - [[-2, -1], 1, Concat, [1]]
  - [-1, 3, RepC3, [256]] # 11
  - [-1, 1, Conv, [256, 1, 1]] # 12

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [2, 1, Conv, [256, 1, 1, None, 1, 1, False]] # 14
  - [[-2, -1], 1, Concat, [1]] # cat backbone P4
  - [-1, 3, RepC3, [256]] # X3 (16), fpn_blocks.1

  - [-1, 1, Conv, [256, 3, 2]] # 17, downsample_convs.0
  - [[-1, 12], 1, Concat, [1]] # cat Y4
  - [-1, 3, RepC3, [256]] # F4 (19), pan_blocks.0

  - [-1, 1, Conv, [256, 3, 2]] # 20, downsample_convs.1
  - [[-1, 7], 1, Concat, [1]] # cat Y5
  - [-1, 3, RepC3, [256]] # F5 (22), pan_blocks.1

  - [[16, 19, 22], 1, RTDETRDecoder, [nc]] # Detect(P3, P4, P5)
