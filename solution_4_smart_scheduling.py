#!/usr/bin/env python3
"""
方案4: 智能调度方案
通过智能调度算法动态管理摄像头，最大化利用有限带宽
"""

import cv2
import time
import threading
import queue
from dataclasses import dataclass
from typing import List, Dict, Optional
import numpy as np

@dataclass
class CameraConfig:
    """摄像头配置"""
    index: int
    priority: int  # 优先级 1-10
    max_fps: int
    resolution: tuple
    active: bool = False

class SmartCameraScheduler:
    """智能摄像头调度器"""
    
    def __init__(self, bandwidth_limit_mbps: float = 400):
        self.bandwidth_limit = bandwidth_limit_mbps
        self.cameras: Dict[int, CameraConfig] = {}
        self.active_cameras: Dict[int, cv2.VideoCapture] = {}
        self.frame_queues: Dict[int, queue.Queue] = {}
        self.running = False
        self.scheduler_thread = None
        
    def add_camera(self, index: int, priority: int, max_fps: int, resolution: tuple):
        """添加摄像头配置"""
        self.cameras[index] = CameraConfig(index, priority, max_fps, resolution)
        self.frame_queues[index] = queue.Queue(maxsize=2)
        
    def estimate_bandwidth(self, config: CameraConfig) -> float:
        """估算摄像头带宽需求 (Mbps)"""
        width, height = config.resolution
        # YUYV格式: 2字节/像素
        bytes_per_frame = width * height * 2
        bytes_per_second = bytes_per_frame * config.max_fps
        mbps = (bytes_per_second * 8) / (1024 * 1024)  # 转换为Mbps
        return mbps
        
    def select_active_cameras(self) -> List[int]:
        """选择当前应该激活的摄像头"""
        # 按优先级排序
        sorted_cameras = sorted(self.cameras.values(), 
                               key=lambda x: x.priority, reverse=True)
        
        selected = []
        total_bandwidth = 0
        
        for camera in sorted_cameras:
            estimated_bw = self.estimate_bandwidth(camera)
            if total_bandwidth + estimated_bw <= self.bandwidth_limit:
                selected.append(camera.index)
                total_bandwidth += estimated_bw
            else:
                # 尝试降低分辨率
                lower_res_config = CameraConfig(
                    camera.index, camera.priority, camera.max_fps, (640, 480)
                )
                lower_bw = self.estimate_bandwidth(lower_res_config)
                
                if total_bandwidth + lower_bw <= self.bandwidth_limit:
                    selected.append(camera.index)
                    total_bandwidth += lower_bw
                    # 更新配置为低分辨率
                    self.cameras[camera.index].resolution = (640, 480)
        
        return selected
        
    def start_camera(self, cam_index: int) -> bool:
        """启动指定摄像头"""
        if cam_index in self.active_cameras:
            return True
            
        try:
            cap = cv2.VideoCapture(cam_index)
            if cap.isOpened():
                config = self.cameras[cam_index]
                width, height = config.resolution
                
                cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)
                cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
                cap.set(cv2.CAP_PROP_FPS, config.max_fps)
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                
                # 测试读取
                ret, frame = cap.read()
                if ret:
                    self.active_cameras[cam_index] = cap
                    config.active = True
                    
                    # 启动捕获线程
                    thread = threading.Thread(
                        target=self._capture_thread, 
                        args=(cam_index,)
                    )
                    thread.daemon = True
                    thread.start()
                    
                    print(f"✅ 摄像头 {cam_index} 启动成功 {width}x{height}")
                    return True
                else:
                    cap.release()
                    return False
            else:
                return False
                
        except Exception as e:
            print(f"❌ 摄像头 {cam_index} 启动失败: {e}")
            return False
    
    def stop_camera(self, cam_index: int):
        """停止指定摄像头"""
        if cam_index in self.active_cameras:
            self.active_cameras[cam_index].release()
            del self.active_cameras[cam_index]
            self.cameras[cam_index].active = False
            print(f"🛑 摄像头 {cam_index} 已停止")
    
    def _capture_thread(self, cam_index: int):
        """摄像头捕获线程"""
        cap = self.active_cameras[cam_index]
        frame_queue = self.frame_queues[cam_index]
        
        while (self.running and 
               cam_index in self.active_cameras and 
               cap.isOpened()):
            try:
                ret, frame = cap.read()
                if ret:
                    # 非阻塞放入队列
                    try:
                        frame_queue.put_nowait(frame)
                    except queue.Full:
                        # 队列满了，丢弃旧帧
                        try:
                            frame_queue.get_nowait()
                            frame_queue.put_nowait(frame)
                        except queue.Empty:
                            pass
                
                time.sleep(1.0 / self.cameras[cam_index].max_fps)
                
            except Exception as e:
                print(f"❌ 摄像头 {cam_index} 捕获错误: {e}")
                break
    
    def get_frame(self, cam_index: int) -> Optional[np.ndarray]:
        """获取摄像头最新帧"""
        if cam_index not in self.frame_queues:
            return None
            
        try:
            return self.frame_queues[cam_index].get_nowait()
        except queue.Empty:
            return None
    
    def start_scheduler(self):
        """启动智能调度器"""
        self.running = True
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop)
        self.scheduler_thread.daemon = True
        self.scheduler_thread.start()
        print("🧠 智能调度器已启动")
    
    def _scheduler_loop(self):
        """调度器主循环"""
        last_schedule_time = 0
        schedule_interval = 5  # 每5秒重新调度
        
        while self.running:
            current_time = time.time()
            
            if current_time - last_schedule_time >= schedule_interval:
                # 重新选择活跃摄像头
                target_cameras = self.select_active_cameras()
                current_active = set(self.active_cameras.keys())
                target_set = set(target_cameras)
                
                # 停止不需要的摄像头
                to_stop = current_active - target_set
                for cam_idx in to_stop:
                    self.stop_camera(cam_idx)
                
                # 启动新需要的摄像头
                to_start = target_set - current_active
                for cam_idx in to_start:
                    self.start_camera(cam_idx)
                
                last_schedule_time = current_time
                
                if to_stop or to_start:
                    print(f"🔄 调度更新: 活跃摄像头 {list(self.active_cameras.keys())}")
            
            time.sleep(1)
    
    def stop_scheduler(self):
        """停止调度器"""
        self.running = False
        
        # 停止所有摄像头
        for cam_idx in list(self.active_cameras.keys()):
            self.stop_camera(cam_idx)
        
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=2)
        
        print("🛑 智能调度器已停止")

def test_smart_scheduler():
    """测试智能调度器"""
    print("🧪 测试智能调度器")
    print("=" * 50)
    
    # 创建调度器
    scheduler = SmartCameraScheduler(bandwidth_limit_mbps=450)
    
    # 添加摄像头配置
    camera_configs = [
        (0, 10, 30, (1280, 720)),  # 高优先级
        (2, 9, 30, (1280, 720)),   # 高优先级
        (4, 5, 30, (1280, 720)),   # 中优先级
        (6, 5, 30, (1280, 720)),   # 中优先级
    ]
    
    for index, priority, fps, resolution in camera_configs:
        scheduler.add_camera(index, priority, fps, resolution)
        bw = scheduler.estimate_bandwidth(scheduler.cameras[index])
        print(f"📷 摄像头 {index}: 优先级 {priority}, 预估带宽 {bw:.1f} Mbps")
    
    # 启动调度器
    scheduler.start_scheduler()
    
    try:
        # 运行10秒
        print(f"\n⏱️  运行10秒，观察调度行为...")
        for i in range(10):
            time.sleep(1)
            
            # 显示当前状态
            active_count = len(scheduler.active_cameras)
            total_bw = sum(scheduler.estimate_bandwidth(scheduler.cameras[idx]) 
                          for idx in scheduler.active_cameras.keys())
            
            print(f"   第{i+1}秒: {active_count}个摄像头活跃, 总带宽 {total_bw:.1f} Mbps")
            
            # 测试获取帧
            for cam_idx in scheduler.active_cameras.keys():
                frame = scheduler.get_frame(cam_idx)
                if frame is not None:
                    print(f"     📸 摄像头 {cam_idx}: 获得帧 {frame.shape}")
        
        # 动态调整优先级测试
        print(f"\n🔄 动态调整优先级测试...")
        scheduler.cameras[4].priority = 15  # 提高摄像头4优先级
        scheduler.cameras[0].priority = 3   # 降低摄像头0优先级
        
        time.sleep(6)  # 等待调度器重新调度
        
        print(f"   调整后活跃摄像头: {list(scheduler.active_cameras.keys())}")
        
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
    
    finally:
        scheduler.stop_scheduler()

def generate_streamlit_integration():
    """生成Streamlit集成代码"""
    print(f"\n💻 Streamlit集成代码")
    print("=" * 50)
    
    integration_code = '''
# 在 Inference 类中添加智能调度支持

class InferenceWithScheduler(Inference):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.scheduler = SmartCameraScheduler(bandwidth_limit_mbps=450)
        
    def setup_cameras_smart(self):
        """使用智能调度器设置摄像头"""
        # 配置摄像头优先级
        priorities = {0: 10, 2: 9, 4: 6, 6: 5}  # 可在UI中调整
        
        for i, cam_idx in enumerate(self.camera_indices):
            priority = priorities.get(cam_idx, 5)
            self.scheduler.add_camera(
                cam_idx, priority, 30, (self.frame_width, self.frame_height)
            )
        
        # 启动调度器
        self.scheduler.start_scheduler()
        return True
    
    def get_camera_frame(self, cam_idx):
        """获取摄像头帧"""
        return self.scheduler.get_frame(cam_idx)
    
    def update_camera_priority(self, cam_idx, new_priority):
        """动态更新摄像头优先级"""
        if cam_idx in self.scheduler.cameras:
            self.scheduler.cameras[cam_idx].priority = new_priority
            self.st.success(f"摄像头 {cam_idx} 优先级更新为 {new_priority}")

# 在sidebar中添加优先级控制
def sidebar_with_priority_control(self):
    # ... 原有代码 ...
    
    if self.source == "multi-webcam":
        self.st.sidebar.subheader("📊 摄像头优先级")
        
        for cam_idx in self.camera_indices:
            current_priority = self.scheduler.cameras.get(cam_idx, {}).priority if hasattr(self, 'scheduler') else 5
            new_priority = self.st.sidebar.slider(
                f"摄像头 {cam_idx} 优先级", 
                1, 10, current_priority,
                key=f"priority_{cam_idx}"
            )
            
            if hasattr(self, 'scheduler') and new_priority != current_priority:
                self.update_camera_priority(cam_idx, new_priority)
'''
    
    with open('streamlit_smart_scheduler_integration.py', 'w') as f:
        f.write(integration_code)
    
    print("✅ 已生成 streamlit_smart_scheduler_integration.py")

def main():
    print("🔄 USB带宽限制 - 智能调度方案")
    print("=" * 60)
    
    print("""
💡 智能调度方案特点:
- 动态选择最重要的摄像头
- 自动调整分辨率以适应带宽
- 支持优先级动态调整
- 最大化利用有限带宽
- 无需额外硬件投资

🎯 适用场景:
- 不同摄像头重要性不同
- 需要灵活的摄像头管理
- 预算有限，无法购买额外硬件
- 希望软件自动优化性能
    """)
    
    # 测试调度器
    test_smart_scheduler()
    
    # 生成集成代码
    generate_streamlit_integration()
    
    print(f"\n✅ 智能调度方案测试完成!")
    print(f"\n🎯 方案优势:")
    print(f"   1. 无需额外硬件成本")
    print(f"   2. 自动优化带宽使用")
    print(f"   3. 支持动态优先级调整")
    print(f"   4. 可与其他方案结合使用")

if __name__ == "__main__":
    main()
