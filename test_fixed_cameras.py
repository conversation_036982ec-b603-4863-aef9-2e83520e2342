#!/usr/bin/env python3
"""
Test the fixed camera implementation
"""

import cv2
import time
import threading
import numpy as np

def test_sequential_initialization():
    """Test sequential camera initialization like in the fixed code"""
    print("🔧 Testing Sequential Camera Initialization")
    print("-" * 50)
    
    camera_indices = [0, 2, 4, 6]
    cameras = []
    successful_cameras = 0
    
    for i, cam_idx in enumerate(camera_indices):
        print(f"\n📷 Initializing Camera {cam_idx}...")
        
        try:
            # Use default backend like in fixed code
            cap = cv2.VideoCapture(cam_idx)
            
            if cap.isOpened():
                # Set buffer size
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                
                # Set resolution
                cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
                cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
                
                # Give camera time to adjust
                time.sleep(0.1)
                
                # Test frame capture multiple times
                test_success = False
                for attempt in range(3):
                    ret, test_frame = cap.read()
                    if ret and test_frame is not None and test_frame.size > 0:
                        test_success = True
                        break
                    time.sleep(0.05)
                
                if test_success:
                    actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                    
                    cameras.append((cam_idx, cap))
                    successful_cameras += 1
                    
                    print(f"  ✅ Success: {actual_width}x{actual_height}")
                else:
                    print(f"  ❌ Cannot read frames reliably")
                    cap.release()
            else:
                print(f"  ❌ Cannot open camera")
            
            # Delay between initializations
            time.sleep(0.2)
            
        except Exception as e:
            print(f"  ❌ Exception: {e}")
    
    print(f"\n📊 Successfully initialized {successful_cameras}/{len(camera_indices)} cameras")
    
    return cameras

def test_threaded_capture(cameras):
    """Test threaded capture like in the fixed code"""
    print(f"\n🎬 Testing Threaded Capture")
    print("-" * 50)
    
    if not cameras:
        print("❌ No cameras available for testing")
        return
    
    # Shared data
    camera_frames = {}
    camera_running = True
    
    def camera_thread(camera_index, cap):
        """Simplified version of the fixed camera thread"""
        consecutive_failures = 0
        max_failures = 10
        
        while camera_running and cap is not None and cap.isOpened():
            try:
                ret, frame = cap.read()
                if ret and frame is not None and frame.size > 0:
                    camera_frames[camera_index] = frame.copy()
                    consecutive_failures = 0
                else:
                    consecutive_failures += 1
                    if consecutive_failures >= max_failures:
                        print(f"❌ Camera {camera_index} failed {max_failures} times")
                        break
                
                time.sleep(0.03)  # ~30 FPS
            except Exception as e:
                consecutive_failures += 1
                print(f"❌ Camera {camera_index} error: {e}")
                if consecutive_failures >= max_failures:
                    break
                time.sleep(0.1)
    
    # Start threads
    threads = []
    for cam_idx, cap in cameras:
        thread = threading.Thread(target=camera_thread, args=(cam_idx, cap))
        thread.daemon = True
        thread.start()
        threads.append(thread)
        print(f"✅ Started thread for camera {cam_idx}")
    
    # Monitor for 5 seconds
    print(f"\n📊 Monitoring for 5 seconds...")
    start_time = time.time()
    frame_counts = {cam_idx: 0 for cam_idx, _ in cameras}
    
    while time.time() - start_time < 5:
        current_frames = len(camera_frames)
        for cam_idx in camera_frames:
            frame_counts[cam_idx] += 1
        
        print(f"\r  Active cameras: {current_frames}/{len(cameras)}, Frames: {dict(frame_counts)}", end="")
        time.sleep(0.1)
    
    # Stop threads
    camera_running = False
    for thread in threads:
        thread.join(timeout=1.0)
    
    # Results
    elapsed = time.time() - start_time
    print(f"\n\n📈 Final Results:")
    for cam_idx, count in frame_counts.items():
        fps = count / elapsed if elapsed > 0 else 0
        print(f"  Camera {cam_idx}: {count} frames ({fps:.1f} FPS)")
    
    # Cleanup
    for cam_idx, cap in cameras:
        cap.release()

def test_resolution_changes():
    """Test resolution changing"""
    print(f"\n📐 Testing Resolution Changes")
    print("-" * 50)
    
    cam_idx = 0  # Test with camera 0
    
    resolutions = [
        (640, 480),
        (1280, 720),
        (1920, 1080),
        (640, 480)  # Back to original
    ]
    
    cap = cv2.VideoCapture(cam_idx)
    if not cap.isOpened():
        print(f"❌ Cannot open camera {cam_idx} for resolution test")
        return
    
    for width, height in resolutions:
        print(f"\n  Setting resolution to {width}x{height}...")
        
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
        
        time.sleep(0.1)  # Give time to adjust
        
        actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        ret, frame = cap.read()
        if ret:
            print(f"    ✅ Set to {actual_width}x{actual_height}, frame shape: {frame.shape}")
        else:
            print(f"    ❌ Set to {actual_width}x{actual_height}, but cannot read frame")
    
    cap.release()

def main():
    print("🧪 Testing Fixed Camera Implementation")
    print("=" * 60)
    
    # Test 1: Sequential initialization
    cameras = test_sequential_initialization()
    
    # Test 2: Threaded capture
    if cameras:
        test_threaded_capture(cameras)
    
    # Test 3: Resolution changes
    test_resolution_changes()
    
    print(f"\n✅ Testing completed!")
    
    if len(cameras) == 4:
        print("🎉 All 4 cameras working with the fixed implementation!")
    elif len(cameras) >= 2:
        print(f"✅ {len(cameras)} cameras working - partial success")
    else:
        print("⚠️  Limited camera functionality")

if __name__ == "__main__":
    main()
