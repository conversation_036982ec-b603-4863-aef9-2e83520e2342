#!/usr/bin/env python3
"""
方案1: 软件优化解决USB带宽限制
通过降低帧率、分辨率、压缩格式来减少带宽需求
"""

import cv2
import time
import threading
import numpy as np

def test_bandwidth_optimization():
    """测试不同的带宽优化策略"""
    print("🔧 方案1: 软件优化测试")
    print("=" * 50)
    
    camera_indices = [0, 2, 4, 6]
    
    # 策略1: 降低分辨率
    strategies = [
        {
            "name": "策略1: 低分辨率高帧率",
            "width": 640, "height": 480, 
            "fps": 30, "format": None,
            "expected_bandwidth": "110 Mbps × 4 = 440 Mbps"
        },
        {
            "name": "策略2: 中分辨率低帧率", 
            "width": 1280, "height": 720,
            "fps": 10, "format": None,
            "expected_bandwidth": "147 Mbps × 4 = 588 Mbps"
        },
        {
            "name": "策略3: MJPG压缩格式",
            "width": 1280, "height": 720,
            "fps": 15, "format": "MJPG",
            "expected_bandwidth": "50-100 Mbps × 4 = 200-400 Mbps"
        },
        {
            "name": "策略4: 交替采样",
            "width": 1280, "height": 720,
            "fps": 30, "format": None,
            "expected_bandwidth": "442 Mbps ÷ 2 = 221 Mbps (轮流)"
        }
    ]
    
    for strategy in strategies:
        print(f"\n📊 {strategy['name']}")
        print(f"   配置: {strategy['width']}x{strategy['height']} @ {strategy['fps']}fps")
        print(f"   预期带宽: {strategy['expected_bandwidth']}")
        
        if strategy['name'].startswith("策略4"):
            test_alternating_sampling(camera_indices, strategy)
        else:
            test_simultaneous_capture(camera_indices, strategy)

def test_simultaneous_capture(camera_indices, strategy):
    """测试同时捕获策略"""
    cameras = {}
    
    for cam_idx in camera_indices:
        try:
            cap = cv2.VideoCapture(cam_idx)
            if cap.isOpened():
                # 设置参数
                cap.set(cv2.CAP_PROP_FRAME_WIDTH, strategy['width'])
                cap.set(cv2.CAP_PROP_FRAME_HEIGHT, strategy['height'])
                cap.set(cv2.CAP_PROP_FPS, strategy['fps'])
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                
                if strategy['format'] == "MJPG":
                    cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
                
                # 测试读取
                ret, frame = cap.read()
                if ret:
                    cameras[cam_idx] = cap
                    print(f"   ✅ 摄像头 {cam_idx}: 初始化成功")
                else:
                    cap.release()
                    print(f"   ❌ 摄像头 {cam_idx}: 无法读取帧")
            else:
                print(f"   ❌ 摄像头 {cam_idx}: 无法打开")
        except Exception as e:
            print(f"   ❌ 摄像头 {cam_idx}: 异常 {e}")
    
    if cameras:
        # 测试5秒
        print(f"   📈 测试 {len(cameras)} 个摄像头，持续5秒...")
        
        frame_counts = {cam_idx: 0 for cam_idx in cameras.keys()}
        start_time = time.time()
        
        while time.time() - start_time < 5:
            for cam_idx, cap in cameras.items():
                ret, frame = cap.read()
                if ret:
                    frame_counts[cam_idx] += 1
            time.sleep(0.01)
        
        elapsed = time.time() - start_time
        for cam_idx, count in frame_counts.items():
            fps = count / elapsed
            print(f"   📊 摄像头 {cam_idx}: {count} 帧 ({fps:.1f} FPS)")
    
    # 清理
    for cap in cameras.values():
        cap.release()

def test_alternating_sampling(camera_indices, strategy):
    """测试交替采样策略"""
    print("   🔄 交替采样模式: 每次只激活2个摄像头")
    
    # 分组: [0,2] 和 [4,6]
    groups = [[0, 2], [4, 6]]
    
    for i, group in enumerate(groups):
        print(f"   📷 测试组 {i+1}: 摄像头 {group}")
        
        cameras = {}
        for cam_idx in group:
            try:
                cap = cv2.VideoCapture(cam_idx)
                if cap.isOpened():
                    cap.set(cv2.CAP_PROP_FRAME_WIDTH, strategy['width'])
                    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, strategy['height'])
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    
                    ret, frame = cap.read()
                    if ret:
                        cameras[cam_idx] = cap
                        print(f"     ✅ 摄像头 {cam_idx}: 成功")
                    else:
                        cap.release()
                        print(f"     ❌ 摄像头 {cam_idx}: 读取失败")
                else:
                    print(f"     ❌ 摄像头 {cam_idx}: 打开失败")
            except Exception as e:
                print(f"     ❌ 摄像头 {cam_idx}: 异常 {e}")
        
        # 测试这组摄像头
        if cameras:
            frame_counts = {cam_idx: 0 for cam_idx in cameras.keys()}
            start_time = time.time()
            
            while time.time() - start_time < 2:  # 每组测试2秒
                for cam_idx, cap in cameras.items():
                    ret, frame = cap.read()
                    if ret:
                        frame_counts[cam_idx] += 1
                time.sleep(0.01)
            
            elapsed = time.time() - start_time
            for cam_idx, count in frame_counts.items():
                fps = count / elapsed
                print(f"     📊 摄像头 {cam_idx}: {fps:.1f} FPS")
        
        # 清理
        for cap in cameras.values():
            cap.release()

def generate_optimization_code():
    """生成优化代码示例"""
    print(f"\n💻 代码实现示例")
    print("=" * 50)
    
    code_examples = {
        "低分辨率模式": '''
# 在 setup_cameras 方法中添加
def setup_cameras_low_res(self):
    # 强制使用低分辨率以支持4摄像头
    self.frame_width = 640
    self.frame_height = 480
    
    for cam_idx in self.camera_indices:
        cap = cv2.VideoCapture(cam_idx)
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 减少缓冲
        ''',
        
        "MJPG压缩模式": '''
# 使用MJPG压缩减少带宽
def setup_cameras_mjpg(self):
    for cam_idx in self.camera_indices:
        cap = cv2.VideoCapture(cam_idx)
        cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
        ''',
        
        "交替采样模式": '''
# 轮流激活摄像头组
def alternating_camera_mode(self):
    groups = [[0, 2], [4, 6]]
    current_group = 0
    
    while self.camera_running:
        active_cameras = groups[current_group]
        # 只处理当前组的摄像头
        for cam_idx in active_cameras:
            # 处理帧...
        
        current_group = (current_group + 1) % 2  # 切换组
        time.sleep(1)  # 每秒切换
        '''
    }
    
    for name, code in code_examples.items():
        print(f"\n📝 {name}:")
        print(code)

def main():
    print("🔧 USB带宽限制 - 软件优化方案")
    print("=" * 60)
    
    print("""
📋 当前问题分析:
- USB 2.0 Hub 总带宽: 480 Mbps
- 4个摄像头 1280x720@30fps 需求: 1768 Mbps
- 带宽不足: 1768 - 480 = 1288 Mbps 缺口

🎯 软件优化目标:
- 将总带宽需求降低到 < 480 Mbps
- 保持4个摄像头同时工作
- 尽可能保持图像质量
    """)
    
    # 运行测试
    test_bandwidth_optimization()
    
    # 生成代码示例
    generate_optimization_code()
    
    print(f"\n✅ 软件优化方案测试完成!")
    print(f"\n💡 推荐策略:")
    print(f"   1. 使用 640x480 分辨率 (最稳定)")
    print(f"   2. 启用 MJPG 压缩 (平衡质量和带宽)")
    print(f"   3. 交替采样模式 (保持高分辨率)")

if __name__ == "__main__":
    main()
