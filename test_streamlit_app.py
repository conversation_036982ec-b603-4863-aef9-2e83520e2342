#!/usr/bin/env python3
"""
Quick test to verify the Streamlit app can initialize properly
"""

import sys
from pathlib import Path

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent))

def test_import():
    """Test if we can import the enhanced inference class"""
    try:
        from ultralytics.solutions.streamlit_inference import Inference
        print("✅ Successfully imported Inference class")
        return True
    except Exception as e:
        print(f"❌ Failed to import Inference class: {e}")
        return False

def test_initialization():
    """Test if we can initialize the Inference class"""
    try:
        from ultralytics.solutions.streamlit_inference import Inference
        
        # Test basic initialization
        inf = Inference()
        print("✅ Successfully initialized Inference class")
        
        # Check default camera configuration
        print(f"📊 Default configuration:")
        print(f"  - Number of cameras: {inf.num_cameras}")
        print(f"  - Camera indices: {inf.camera_indices}")
        print(f"  - Default resolution: {inf.frame_width}x{inf.frame_height}")
        
        return True
    except Exception as e:
        print(f"❌ Failed to initialize Inference class: {e}")
        return False

def test_camera_setup():
    """Test camera setup functionality"""
    try:
        from ultralytics.solutions.streamlit_inference import Inference
        
        inf = Inference()
        
        # Test camera setup (without actually starting cameras)
        print("🎥 Testing camera setup logic...")
        
        # This would normally set up cameras, but we'll just test the method exists
        if hasattr(inf, 'setup_cameras'):
            print("✅ setup_cameras method exists")
        else:
            print("❌ setup_cameras method missing")
            return False
        
        if hasattr(inf, 'multi_camera_inference'):
            print("✅ multi_camera_inference method exists")
        else:
            print("❌ multi_camera_inference method missing")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Error testing camera setup: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing Enhanced Streamlit Inference App")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_import),
        ("Initialization Test", test_initialization),
        ("Camera Setup Test", test_camera_setup),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name}...")
        if test_func():
            passed += 1
            print(f"✅ {test_name} PASSED")
        else:
            print(f"❌ {test_name} FAILED")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Your enhanced Streamlit app is ready to use.")
        print("\n🚀 To start the application:")
        print("   streamlit run ultralytics/solutions/streamlit_inference.py")
        print("\n📋 Configuration for your cameras:")
        print("   - Source: multi-webcam")
        print("   - Number of cameras: 4")
        print("   - Camera indices: 0,2,4,6")
        print("   - Recommended resolution: 1280x720")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
