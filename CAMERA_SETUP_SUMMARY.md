# 🎥 您的多摄像头设置总结

## 📊 摄像头配置确认

经过详细测试，您的系统配置如下：

### ✅ 可用摄像头
- **摄像头 0**: USB HD Camera ✅ (单独使用: 1280x720@30fps, 同时使用: 可靠)
- **摄像头 2**: USB HD Camera ✅ (单独使用: 1280x720@30fps, 同时使用: 可靠)
- **摄像头 4**: USB HD Camera ⚠️ (单独使用: 正常, 同时使用: USB带宽限制)
- **摄像头 6**: USB HD Camera ⚠️ (单独使用: 正常, 同时使用: USB带宽限制)

### 🔍 重要发现
- **USB带宽限制**: 由于USB控制器带宽限制，摄像头4和6无法与0、2同时工作
- **最佳配置**: 摄像头0和2可以稳定地同时工作
- **单独使用**: 所有4个摄像头都可以单独正常工作

### 🔧 技术规格
- **总数**: 4个USB HD摄像头
- **连接**: USB 3.0端口 (2.3.1, 2.3.2, 2.3.3, 2.3.4)
- **格式**: MJPG/YUYV支持
- **最佳分辨率**: 1280x720 @ 30fps (理论) / ~12fps (实际多摄像头)
- **设备节点**: /dev/video0, /dev/video2, /dev/video4, /dev/video6

## 🚀 快速开始

### 1. 启动多摄像头应用
```bash
cd /home/<USER>/Downloads/ultralytics-main
streamlit run ultralytics/solutions/streamlit_inference.py
```

### 2. 配置设置
在Streamlit界面中：

#### 🟢 推荐配置 (最稳定)
1. **Source**: 选择 "multi-webcam"
2. **Camera Configuration**: 选择 "Reliable (0,2)"
3. **Resolution**: 选择 "1280x720"
4. **Model**: 选择 "yolo11n.pt" (快速) 或 "yolo11s.pt" (平衡)

#### 🟡 单摄像头高质量配置
1. **Source**: 选择 "multi-webcam"
2. **Camera Configuration**: 选择 "Single Camera 0" (或2,4,6)
3. **Resolution**: 选择 "1920x1080"
4. **Model**: 可选择更大的模型如 "yolo11m.pt"

### 3. 开始推理
点击 "Start" 按钮开始多摄像头推理

## 🎯 使用示例

### 示例1: 基础4摄像头设置
```bash
streamlit run examples/multi_camera_inference.py
# 默认使用摄像头 0,2,4,6 和 1280x720 分辨率
```

### 示例2: 自定义配置
```bash
streamlit run examples/multi_camera_inference.py -- \
    --cameras 0,2,4,6 \
    --resolution 1280x720 \
    --model yolo11s.pt \
    --conf 0.3
```

### 示例3: 双摄像头设置
```bash
streamlit run examples/multi_camera_inference.py -- \
    --cameras 0,2 \
    --resolution 1920x1080
```

## 🔧 故障排除

### 常见问题解决

1. **摄像头无法打开**
   ```bash
   # 检查摄像头状态
   python test_cameras_mjpg.py
   
   # 检查设备权限
   ls -la /dev/video*
   
   # 确保没有其他程序占用摄像头
   sudo lsof /dev/video*
   ```

2. **帧率较低**
   - 降低分辨率到 640x480
   - 减少同时使用的摄像头数量
   - 使用更轻量的模型 (yolo11n.pt)

3. **内存不足**
   ```bash
   # 监控内存使用
   htop
   
   # 减少摄像头数量或分辨率
   ```

### 性能优化建议

1. **推荐配置**:
   - 2-4摄像头: 1280x720
   - 4摄像头: 640x480 (如果性能不足)
   - 模型: yolo11n.pt (快速) 或 yolo11s.pt (平衡)

2. **硬件要求**:
   - CPU: 多核处理器 (您的NVIDIA设备应该足够)
   - RAM: 8GB+ 
   - USB: 确保使用USB 3.0端口

## 📁 相关文件

- **主应用**: `ultralytics/solutions/streamlit_inference.py`
- **示例脚本**: `examples/multi_camera_inference.py`
- **测试脚本**: `test_cameras_mjpg.py`
- **应用测试**: `test_streamlit_app.py`
- **详细文档**: `docs/multi_camera_inference.md`

## 🎨 界面布局

多摄像头界面将显示：
```
┌─────────────┬─────────────┐
│  Camera 0   │  Camera 2   │
│ Original    │ Original    │
├─────────────┼─────────────┤
│  Camera 0   │  Camera 2   │
│ Predicted   │ Predicted   │
├─────────────┼─────────────┤
│  Camera 4   │  Camera 6   │
│ Original    │ Original    │
├─────────────┼─────────────┤
│  Camera 4   │  Camera 6   │
│ Predicted   │ Predicted   │
└─────────────┴─────────────┘
```

## ✅ 验证清单

在使用前，请确认：
- [ ] 所有4个摄像头都已连接
- [ ] 运行 `python test_cameras_mjpg.py` 确认摄像头工作
- [ ] 运行 `python test_streamlit_app.py` 确认应用正常
- [ ] 安装了所需依赖: `pip install streamlit>=1.29.0 ultralytics opencv-python`

## 🎉 享受您的多摄像头AI推理系统！

您现在拥有一个功能完整的4摄像头YOLO推理系统，支持：
- 实时目标检测
- 多目标跟踪
- 自定义分辨率
- 灵活的摄像头配置
- 直观的Web界面

如有任何问题，请参考故障排除部分或查看详细文档。
