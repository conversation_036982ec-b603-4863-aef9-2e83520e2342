# Multi-Camera YOLO Inference with Streamlit

This enhanced version of the Ultralytics Streamlit inference application supports multiple cameras and custom resolutions, making it perfect for surveillance systems, multi-angle monitoring, and advanced computer vision applications.

## 🚀 New Features

### Multi-Camera Support
- **Simultaneous Processing**: Process up to 8 cameras simultaneously
- **Flexible Camera Selection**: Choose specific camera indices (e.g., 0, 1, 2, 3)
- **Grid Layout**: Automatic grid layout for optimal viewing
- **Independent Tracking**: Each camera can have independent object tracking

### Custom Resolution Support
- **Preset Resolutions**: Quick selection of common resolutions (640x480, 1280x720, 1920x1080)
- **Custom Resolution**: Set any resolution supported by your cameras
- **Per-Camera Configuration**: Each camera uses the same resolution for consistency

### Enhanced UI
- **Organized Layout**: Clean grid layout for multiple camera feeds
- **Real-time Display**: Original and annotated frames side-by-side for each camera
- **Camera Identification**: Clear labeling of each camera feed

## 📋 Requirements

```bash
pip install streamlit>=1.29.0 ultralytics opencv-python
```

## 🎯 Usage

### Basic Multi-Camera Setup

1. **Launch the Application**:
```bash
streamlit run ultralytics/solutions/streamlit_inference.py
```

2. **Configure Multi-Camera Mode**:
   - Select "multi-webcam" from the Source dropdown
   - Set the number of cameras (1-8)
   - Enter camera indices (e.g., "0,1,2,3")
   - Choose resolution preset or set custom resolution

3. **Start Inference**:
   - Click "Start" to begin multi-camera inference
   - Use "Stop" to end the session

### Advanced Configuration

#### Camera Indices
Camera indices correspond to your system's camera devices:
- `0`: Usually the built-in webcam
- `1,2,3...`: External USB cameras or IP cameras
- Check available cameras: `ls /dev/video*` (Linux) or Device Manager (Windows)

#### Resolution Settings
- **640x480**: Good for basic monitoring, low bandwidth
- **1280x720**: HD quality, balanced performance
- **1920x1080**: Full HD, high quality (requires powerful hardware)
- **Custom**: Set any resolution your cameras support

#### Performance Considerations
- **CPU Usage**: More cameras = higher CPU usage
- **Memory**: Higher resolutions require more memory
- **Network**: IP cameras consume network bandwidth
- **Recommended**: Start with 2-4 cameras at 720p for optimal performance

## 🛠️ Configuration Examples

### Example 1: 4-Camera Security System
```python
from ultralytics.solutions.streamlit_inference import Inference

# Create inference instance
inf = Inference(model="yolo11n.pt")

# Configure for 4 cameras at 720p
inf.num_cameras = 4
inf.camera_indices = [0, 1, 2, 3]
inf.frame_width = 1280
inf.frame_height = 720

# Run inference
inf.inference()
```

### Example 2: Dual Camera Setup
```python
# High-resolution dual camera setup
inf = Inference()
inf.num_cameras = 2
inf.camera_indices = [0, 2]  # Skip camera 1
inf.frame_width = 1920
inf.frame_height = 1080
inf.inference()
```

### Example 3: Custom Camera Configuration
```bash
# Using the example script
streamlit run examples/multi_camera_inference.py -- \
    --cameras 0,1,2,3 \
    --resolution 1280x720 \
    --model yolo11s.pt \
    --conf 0.3
```

## 🔧 Troubleshooting

### Common Issues

1. **Camera Not Detected**:
   - Check camera connections
   - Verify camera indices with `ls /dev/video*`
   - Try different USB ports
   - Check camera permissions

2. **Low Frame Rate**:
   - Reduce resolution
   - Decrease number of cameras
   - Use a lighter YOLO model (yolo11n vs yolo11x)
   - Close other applications using cameras

3. **High CPU Usage**:
   - Lower resolution settings
   - Reduce confidence threshold
   - Use GPU acceleration if available
   - Limit number of simultaneous cameras

4. **Memory Issues**:
   - Reduce resolution
   - Decrease number of cameras
   - Close other applications

### Performance Optimization

1. **Hardware Recommendations**:
   - **CPU**: Multi-core processor (Intel i5/i7, AMD Ryzen 5/7)
   - **RAM**: 8GB+ for 4 cameras at 720p
   - **GPU**: NVIDIA GPU for CUDA acceleration (optional)
   - **USB**: USB 3.0 ports for multiple cameras

2. **Software Optimization**:
   - Use appropriate YOLO model size
   - Adjust confidence and IoU thresholds
   - Enable tracking only when needed
   - Monitor system resources

## 📊 Supported Configurations

| Cameras | Resolution | Recommended Hardware | Use Case |
|---------|------------|---------------------|----------|
| 1-2 | 1920x1080 | Mid-range laptop | High-quality monitoring |
| 2-4 | 1280x720 | Desktop PC | Security system |
| 4-6 | 640x480 | High-end workstation | Multi-angle analysis |
| 6-8 | 640x480 | Server-grade hardware | Industrial monitoring |

## 🎨 UI Layout

The multi-camera interface automatically arranges cameras in a grid:
- **1-4 cameras**: 2 columns
- **5-8 cameras**: 3 columns
- Each camera shows original and annotated frames
- Camera indices are clearly labeled

## 🔄 Migration from Single Camera

If you're upgrading from the single-camera version:

1. **Backward Compatibility**: Single camera mode still works exactly as before
2. **New Options**: Additional "multi-webcam" source option
3. **Enhanced Settings**: Resolution controls now available for single cameras too
4. **Same API**: Existing code continues to work without changes

## 📝 License

This enhanced version maintains the same AGPL-3.0 license as the original Ultralytics codebase.
