---
description: Documentation for YOLOE validator classes in Ultralytics, supporting both text and visual prompt embeddings for object detection and segmentation models.
keywords: Y<PERSON><PERSON>, validation, object detection, segmentation, visual prompts, text prompts, embeddings, Ultralytics, YOLOEDetectValidator, YOLOESegValidator, deep learning
---

# Reference for `ultralytics/models/yolo/yoloe/val.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/yoloe/val.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/yoloe/val.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/yoloe/val.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.yolo.yoloe.val.YOLOEDetectValidator

<br><br><hr><br>

## ::: ultralytics.models.yolo.yoloe.val.YOLOESegValidator

<br><br>
