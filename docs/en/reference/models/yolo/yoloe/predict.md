---
description: Documentation for YOLOE visual prompt predictors in Ultralytics, supporting inference with visual prompts for both object detection and segmentation models.
keywords: <PERSON><PERSON><PERSON>, visual prompts, predictors, YOLOEVPDetectPredictor, YOLOEVPSegPredictor, inference, object detection, segmentation, Ultralytics, deep learning
---

# Reference for `ultralytics/models/yolo/yoloe/predict.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/yoloe/predict.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/yoloe/predict.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/yoloe/predict.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.yolo.yoloe.predict.YOLOEVPDetectPredictor

<br><br><hr><br>

## ::: ultralytics.models.yolo.yoloe.predict.YOLOEVPSegPredictor

<br><br>
