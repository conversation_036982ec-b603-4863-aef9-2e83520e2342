---
description: Explore detailed implementations of loss functions for DETR and RT-DETR models in Ultralytics.
keywords: ultralytics, YOLO, DETR, RT-DETR, loss functions, object detection, deep learning, focal loss, varifocal loss, Hungarian matcher
---

# Reference for `ultralytics/models/utils/loss.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/utils/loss.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/utils/loss.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/utils/loss.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.utils.loss.DETRLoss

<br><br><hr><br>

## ::: ultralytics.models.utils.loss.RTDETRDetectionLoss

<br><br>
