---
description: Explore the Fast SAM Predictor in the Ultralytics YOLO framework. Learn about its segmentation prediction tasks, configuration, and post-processing steps.
keywords: Ultralytics, Fast SAM Predictor, YOLO, segmentation, prediction, AI model, non-max suppression, mask prediction, tutorial
---

# Reference for `ultralytics/models/fastsam/predict.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/fastsam/predict.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/fastsam/predict.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/fastsam/predict.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.fastsam.predict.FastSAMPredictor

<br><br>
