---
description: Explore RTDETRTrainer for efficient real-time object detection leveraging Vision Transformers. Learn configuration, dataset handling, and advanced model training.
keywords: RTDETRTrainer, real-time object detection, Vision Transformers, YOLO, RT-DETR model, model training, dataset handling
---

# Reference for `ultralytics/models/rtdetr/train.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/rtdetr/train.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/rtdetr/train.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/rtdetr/train.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.rtdetr.train.RTDETRTrainer

<br><br>
