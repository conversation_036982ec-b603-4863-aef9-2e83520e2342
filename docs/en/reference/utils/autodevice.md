---
description: Discover how to automatically select the most idle GPU using PyTorch and NVML (pynvml) with this AutoDevice script. Ideal for optimizing GPU usage in deep learning and computer vision tasks.
keywords: Ultralytics, AutoDevice, GPU selection, PyTorch, NVML, pynvml, GPU monitoring, CUDA, deep learning, idle GPU, memory utilization, temperature, power usage
---

# Reference for `ultralytics/utils/autodevice.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/autodevice.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/autodevice.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/autodevice.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.utils.autodevice.GPUInfo

<br><br>
