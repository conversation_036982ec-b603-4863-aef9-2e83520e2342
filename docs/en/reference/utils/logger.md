---
description: High-performance console output capture with API/file streaming for YOLO11 training logs.
keywords: ConsoleLogger, console capture, log streaming, API logging, file logging, YOLO11, Ultralytics
---

# Reference for `ultralytics/utils/logger.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/logger.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/logger.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/logger.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.utils.logger.ConsoleLogger

<br><br><hr><br>

## ::: ultralytics.utils.logger.SystemLogger

<br><br>
