---
description: Discover how to automatically estimate the best YOLO batch size for optimal CUDA memory usage in PyTorch using Ultralytics' autobatch utility.
keywords: YOLO batch size, CUDA memory, PyTorch autobatch, Ultralytics, machine learning, optimal batch size, training batch size, YOLO model
---

# Reference for `ultralytics/utils/autobatch.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/autobatch.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/autobatch.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/autobatch.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.utils.autobatch.check_train_batch_size

<br><br><hr><br>

## ::: ultralytics.utils.autobatch.autobatch

<br><br>
