---
description: Explore the integration of Comet callbacks in Ultralytics YOLO, enabling advanced logging and monitoring for your machine learning experiments.
keywords: Ultralytics, YOLO, Comet, callbacks, logging, machine learning, monitoring, integration
---

# Reference for `ultralytics/utils/callbacks/comet.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/comet.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/comet.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/callbacks/comet.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.utils.callbacks.comet._get_comet_mode

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet._get_comet_model_name

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet._get_eval_batch_logging_interval

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet._get_max_image_predictions_to_log

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet._scale_confidence_score

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet._should_log_confusion_matrix

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet._should_log_image_predictions

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet._resume_or_create_experiment

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet._fetch_trainer_metadata

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet._scale_bounding_box_to_original_image_shape

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet._format_ground_truth_annotations_for_detection

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet._format_prediction_annotations

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet._extract_segmentation_annotation

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet._fetch_annotations

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet._create_prediction_metadata_map

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet._log_confusion_matrix

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet._log_images

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet._log_image_predictions

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet._log_plots

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet._log_model

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet._log_image_batches

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet._log_asset

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet._log_table

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet.on_pretrain_routine_start

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet.on_train_epoch_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet.on_fit_epoch_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.comet.on_train_end

<br><br>
