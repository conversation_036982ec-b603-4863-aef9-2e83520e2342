---
description: Platform callback functions for console logging during YOLO11 training lifecycle events.
keywords: platform callbacks, training callbacks, console logging, YOLO11 training, lifecycle events, Ultralytics
---

# Reference for `ultralytics/utils/callbacks/platform.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/platform.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/platform.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/callbacks/platform.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.utils.callbacks.platform.on_pretrain_routine_start

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.platform.on_pretrain_routine_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.platform.on_fit_epoch_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.platform.on_model_save

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.platform.on_train_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.platform.on_train_start

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.platform.on_val_start

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.platform.on_predict_start

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.platform.on_export_start

<br><br>
