---
description: Learn how to split datasets into train, validation, and test subsets using Ultralytics utilities for efficient data preparation.
keywords: dataset splitting, autosplit dataset, training dataset preparation, validation set creation, Ultralytics data tools
---

# Reference for `ultralytics/data/split.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/split.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/split.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/data/split.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.data.split.split_classify_dataset

<br><br><hr><br>

## ::: ultralytics.data.split.autosplit

<br><br>
