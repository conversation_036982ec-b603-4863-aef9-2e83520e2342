#!/usr/bin/env python3
"""
Debug script to identify camera issues
"""

import cv2
import time
import threading

def test_camera_detailed(cam_idx):
    """Detailed test of a single camera"""
    print(f"\n🔍 Detailed test for Camera {cam_idx}")
    print("-" * 40)
    
    # Test different backends
    backends = [
        ("Default", None),
        ("V4L2", cv2.CAP_V4L2),
        ("GSTREAMER", cv2.CAP_GSTREAMER),
    ]
    
    for backend_name, backend in backends:
        print(f"\n  Testing {backend_name} backend:")
        
        try:
            if backend is None:
                cap = cv2.VideoCapture(cam_idx)
            else:
                cap = cv2.VideoCapture(cam_idx, backend)
            
            if cap.isOpened():
                print(f"    ✅ Opened successfully")
                
                # Get initial properties
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                fps = cap.get(cv2.CAP_PROP_FPS)
                fourcc = int(cap.get(cv2.CAP_PROP_FOURCC))
                fourcc_str = "".join([chr((fourcc >> 8 * i) & 0xFF) for i in range(4)])
                
                print(f"    📊 Initial: {width}x{height} @ {fps}fps, format: {fourcc_str}")
                
                # Try to read a frame
                ret, frame = cap.read()
                if ret and frame is not None:
                    print(f"    ✅ Frame read successful: {frame.shape}")
                    
                    # Try setting different resolutions
                    resolutions = [(640, 480), (1280, 720)]
                    for w, h in resolutions:
                        cap.set(cv2.CAP_PROP_FRAME_WIDTH, w)
                        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, h)
                        
                        actual_w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                        actual_h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                        
                        ret, frame = cap.read()
                        if ret:
                            print(f"    ✅ Resolution {w}x{h} -> {actual_w}x{actual_h}: OK")
                        else:
                            print(f"    ❌ Resolution {w}x{h} -> {actual_w}x{actual_h}: Cannot read frame")
                else:
                    print(f"    ❌ Cannot read initial frame")
                
                cap.release()
            else:
                print(f"    ❌ Cannot open camera")
                
        except Exception as e:
            print(f"    ❌ Exception: {e}")

def test_simultaneous_access():
    """Test if cameras can be accessed simultaneously"""
    print(f"\n🎬 Testing simultaneous access to cameras 0,2,4,6")
    print("-" * 50)
    
    cameras = {}
    camera_indices = [0, 2, 4, 6]
    
    # Try to open all cameras
    for cam_idx in camera_indices:
        try:
            cap = cv2.VideoCapture(cam_idx, cv2.CAP_V4L2)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret:
                    cameras[cam_idx] = cap
                    print(f"✅ Camera {cam_idx}: Opened and reading")
                else:
                    print(f"❌ Camera {cam_idx}: Opened but cannot read")
                    cap.release()
            else:
                print(f"❌ Camera {cam_idx}: Cannot open")
        except Exception as e:
            print(f"❌ Camera {cam_idx}: Exception - {e}")
    
    if len(cameras) >= 2:
        print(f"\n📊 Testing {len(cameras)} cameras simultaneously for 3 seconds...")
        
        def capture_frames(cam_idx, cap, results):
            count = 0
            start_time = time.time()
            while time.time() - start_time < 3:
                ret, frame = cap.read()
                if ret:
                    count += 1
                time.sleep(0.01)
            results[cam_idx] = count
        
        # Start threads
        threads = []
        results = {}
        
        for cam_idx, cap in cameras.items():
            thread = threading.Thread(target=capture_frames, args=(cam_idx, cap, results))
            thread.start()
            threads.append(thread)
        
        # Wait for completion
        for thread in threads:
            thread.join()
        
        # Report results
        for cam_idx, count in results.items():
            fps = count / 3
            print(f"  Camera {cam_idx}: {count} frames ({fps:.1f} FPS)")
    
    # Cleanup
    for cap in cameras.values():
        cap.release()

def test_resource_conflicts():
    """Test for resource conflicts"""
    print(f"\n🔒 Testing for resource conflicts")
    print("-" * 40)
    
    import subprocess
    import os
    
    # Check if any processes are using video devices
    try:
        result = subprocess.run(['lsof', '/dev/video0', '/dev/video2', '/dev/video4', '/dev/video6'], 
                              capture_output=True, text=True)
        if result.stdout:
            print("⚠️  Processes using video devices:")
            print(result.stdout)
        else:
            print("✅ No processes currently using video devices")
    except:
        print("❓ Cannot check process usage (lsof not available)")
    
    # Check permissions
    for cam_idx in [0, 2, 4, 6]:
        device_path = f"/dev/video{cam_idx}"
        if os.path.exists(device_path):
            stat = os.stat(device_path)
            print(f"📁 {device_path}: permissions {oct(stat.st_mode)[-3:]}")
        else:
            print(f"❌ {device_path}: does not exist")

def main():
    print("🐛 Camera Debug Tool")
    print("=" * 50)
    
    # Test each camera individually
    for cam_idx in [0, 2, 4, 6]:
        test_camera_detailed(cam_idx)
    
    # Test simultaneous access
    test_simultaneous_access()
    
    # Test for conflicts
    test_resource_conflicts()
    
    print(f"\n✅ Debug completed!")
    print("\n💡 Recommendations:")
    print("1. If cameras 4,6 fail with V4L2, try default backend")
    print("2. If permission errors, check user groups: groups $USER")
    print("3. If resource conflicts, close other camera applications")
    print("4. Try cameras one by one to isolate issues")

if __name__ == "__main__":
    main()
