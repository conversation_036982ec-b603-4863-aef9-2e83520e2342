#!/usr/bin/env python3
"""
Multi-Camera YOLO Inference Example

This script demonstrates how to use the enhanced Streamlit inference application
with multiple cameras and custom resolutions.

Usage:
    # Run with default settings (4 cameras)
    streamlit run examples/multi_camera_inference.py

    # Run with custom model
    streamlit run examples/multi_camera_inference.py -- --model yolo11n.pt

    # Run with specific camera configuration
    streamlit run examples/multi_camera_inference.py -- --cameras 0,1,2,3 --resolution 1280x720

Requirements:
    - Multiple USB cameras or IP cameras
    - streamlit>=1.29.0
    - ultralytics
    - opencv-python

Author: Ultralytics
License: AGPL-3.0
"""

import argparse
import sys
from pathlib import Path

# Add the parent directory to the path to import ultralytics
sys.path.append(str(Path(__file__).parent.parent))

from ultralytics.solutions.streamlit_inference import Inference


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Multi-Camera YOLO Inference")
    parser.add_argument(
        "--model", 
        type=str, 
        default=None, 
        help="Path to YOLO model file (default: yolo11n.pt)"
    )
    parser.add_argument(
        "--cameras",
        type=str,
        default="0,2,4,6",
        help="Comma-separated list of camera indices (default: 0,2,4,6 - all 4 cameras)"
    )
    parser.add_argument(
        "--resolution", 
        type=str, 
        default="640x480", 
        help="Camera resolution in format WIDTHxHEIGHT (default: 640x480)"
    )
    parser.add_argument(
        "--conf", 
        type=float, 
        default=0.25, 
        help="Confidence threshold (default: 0.25)"
    )
    parser.add_argument(
        "--iou", 
        type=float, 
        default=0.45, 
        help="IoU threshold (default: 0.45)"
    )
    return parser.parse_args()


def main():
    """Main function to run multi-camera inference."""
    args = parse_args()
    
    # Parse camera indices
    try:
        camera_indices = [int(x.strip()) for x in args.cameras.split(",")]
    except ValueError:
        print("Error: Invalid camera indices format. Use comma-separated integers.")
        sys.exit(1)
    
    # Parse resolution
    try:
        width, height = map(int, args.resolution.split("x"))
    except ValueError:
        print("Error: Invalid resolution format. Use WIDTHxHEIGHT (e.g., 1280x720)")
        sys.exit(1)
    
    print(f"Starting multi-camera inference with:")
    print(f"  Model: {args.model or 'yolo11n.pt'}")
    print(f"  Cameras: {camera_indices}")
    print(f"  Resolution: {width}x{height}")
    print(f"  Confidence: {args.conf}")
    print(f"  IoU: {args.iou}")
    print("\nOpen your browser and navigate to the Streamlit URL to start inference.")
    
    # Create inference instance with custom configuration
    inference = Inference(model=args.model)
    
    # Override default settings
    inference.camera_indices = camera_indices
    inference.num_cameras = len(camera_indices)
    inference.frame_width = width
    inference.frame_height = height
    inference.conf = args.conf
    inference.iou = args.iou
    
    # Run inference
    inference.inference()


if __name__ == "__main__":
    main()
