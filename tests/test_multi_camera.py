#!/usr/bin/env python3
"""
Test script for multi-camera functionality

This script tests the multi-camera inference capabilities without requiring Streamlit UI.
Useful for debugging camera connections and basic functionality.

Usage:
    python tests/test_multi_camera.py
"""

import cv2
import sys
import time
from pathlib import Path

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent.parent))

from ultralytics import <PERSON><PERSON><PERSON>


def test_camera_availability():
    """Test which cameras are available on the system."""
    print("Testing camera availability...")
    available_cameras = []
    
    for i in range(8):  # Test cameras 0-7
        cap = cv2.VideoCapture(i)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret:
                height, width = frame.shape[:2]
                print(f"✅ Camera {i}: Available ({width}x{height})")
                available_cameras.append(i)
            else:
                print(f"❌ Camera {i}: Cannot read frames")
            cap.release()
        else:
            print(f"❌ Camera {i}: Cannot open")
    
    return available_cameras


def test_resolution_setting(camera_indices, target_width=640, target_height=480):
    """Test setting custom resolution on cameras."""
    print(f"\nTesting resolution setting to {target_width}x{target_height}...")
    
    for cam_idx in camera_indices:
        cap = cv2.VideoCapture(cam_idx)
        if cap.isOpened():
            # Set resolution
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, target_width)
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, target_height)
            
            # Get actual resolution
            actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            if actual_width == target_width and actual_height == target_height:
                print(f"✅ Camera {cam_idx}: Resolution set successfully ({actual_width}x{actual_height})")
            else:
                print(f"⚠️  Camera {cam_idx}: Resolution partially set ({actual_width}x{actual_height})")
            
            cap.release()
        else:
            print(f"❌ Camera {cam_idx}: Cannot open for resolution test")


def test_multi_camera_capture(camera_indices, duration=5):
    """Test simultaneous capture from multiple cameras."""
    print(f"\nTesting multi-camera capture for {duration} seconds...")
    
    # Initialize cameras
    cameras = {}
    for cam_idx in camera_indices:
        cap = cv2.VideoCapture(cam_idx)
        if cap.isOpened():
            cameras[cam_idx] = cap
            print(f"✅ Camera {cam_idx}: Initialized")
        else:
            print(f"❌ Camera {cam_idx}: Failed to initialize")
    
    if not cameras:
        print("❌ No cameras available for testing")
        return
    
    # Capture frames
    start_time = time.time()
    frame_counts = {cam_idx: 0 for cam_idx in cameras.keys()}
    
    try:
        while time.time() - start_time < duration:
            for cam_idx, cap in cameras.items():
                ret, frame = cap.read()
                if ret:
                    frame_counts[cam_idx] += 1
                    # Optional: Display frame (uncomment for visual test)
                    # cv2.imshow(f'Camera {cam_idx}', frame)
            
            # Small delay to prevent excessive CPU usage
            time.sleep(0.01)
            
            # Break on 'q' key (if displaying frames)
            # if cv2.waitKey(1) & 0xFF == ord('q'):
            #     break
    
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    
    finally:
        # Cleanup
        for cap in cameras.values():
            cap.release()
        cv2.destroyAllWindows()
    
    # Report results
    print("\nCapture Results:")
    for cam_idx, count in frame_counts.items():
        fps = count / duration
        print(f"Camera {cam_idx}: {count} frames ({fps:.1f} FPS)")


def test_yolo_inference(camera_indices, model_name="yolo11n.pt"):
    """Test YOLO inference on multiple cameras."""
    print(f"\nTesting YOLO inference with {model_name}...")
    
    try:
        model = YOLO(model_name)
        print(f"✅ Model {model_name} loaded successfully")
    except Exception as e:
        print(f"❌ Failed to load model {model_name}: {e}")
        return
    
    # Test inference on each camera
    for cam_idx in camera_indices[:2]:  # Limit to 2 cameras for quick test
        cap = cv2.VideoCapture(cam_idx)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret:
                try:
                    results = model(frame, conf=0.25, verbose=False)
                    detections = len(results[0].boxes) if results[0].boxes is not None else 0
                    print(f"✅ Camera {cam_idx}: Inference successful ({detections} detections)")
                except Exception as e:
                    print(f"❌ Camera {cam_idx}: Inference failed - {e}")
            else:
                print(f"❌ Camera {cam_idx}: Cannot read frame for inference")
            cap.release()
        else:
            print(f"❌ Camera {cam_idx}: Cannot open for inference test")


def main():
    """Main test function."""
    print("🔍 Multi-Camera System Test")
    print("=" * 50)
    
    # Test 1: Check available cameras
    available_cameras = test_camera_availability()
    
    if not available_cameras:
        print("\n❌ No cameras found. Please check your camera connections.")
        return
    
    print(f"\n📊 Found {len(available_cameras)} available camera(s): {available_cameras}")
    
    # Test 2: Resolution setting
    test_resolution_setting(available_cameras[:4])  # Test up to 4 cameras
    
    # Test 3: Multi-camera capture
    if len(available_cameras) >= 2:
        test_multi_camera_capture(available_cameras[:4], duration=3)
    else:
        print("\n⚠️  Skipping multi-camera capture test (need at least 2 cameras)")
    
    # Test 4: YOLO inference
    test_yolo_inference(available_cameras[:2])
    
    print("\n✅ Multi-camera test completed!")
    print("\nRecommendations:")
    if len(available_cameras) >= 4:
        print("- Your system supports 4+ cameras - perfect for multi-camera inference!")
    elif len(available_cameras) >= 2:
        print("- Your system supports 2+ cameras - good for dual-camera setups")
    else:
        print("- Consider connecting additional cameras for multi-camera functionality")
    
    print(f"- Available cameras: {available_cameras}")
    print("- Run 'streamlit run ultralytics/solutions/streamlit_inference.py' to start the web interface")


if __name__ == "__main__":
    main()
