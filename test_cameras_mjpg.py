#!/usr/bin/env python3
"""
Test cameras with MJPG format support
"""

import cv2
import time

def test_camera_with_mjpg(cam_idx):
    """Test a single camera with MJPG format"""
    print(f"\n🎥 Testing Camera {cam_idx}")
    
    cap = cv2.VideoCapture(cam_idx)
    if not cap.isOpened():
        print(f"❌ Cannot open camera {cam_idx}")
        return False
    
    # Try different approaches
    approaches = [
        ("Default", {}),
        ("MJPG", {"fourcc": cv2.VideoWriter_fourcc(*'MJPG')}),
        ("CAP_V4L2", {"api": cv2.CAP_V4L2}),
        ("V4L2+MJPG", {"api": cv2.CAP_V4L2, "fourcc": cv2.VideoWriter_fourcc(*'MJPG')}),
    ]
    
    for name, params in approaches:
        cap.release()
        
        if "api" in params:
            cap = cv2.VideoCapture(cam_idx, params["api"])
        else:
            cap = cv2.VideoCapture(cam_idx)
        
        if not cap.isOpened():
            print(f"  ❌ {name}: Cannot open")
            continue
        
        # Set format if specified
        if "fourcc" in params:
            cap.set(cv2.CAP_PROP_FOURCC, params["fourcc"])
        
        # Set resolution
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
        
        # Try to read frame
        ret, frame = cap.read()
        if ret and frame is not None:
            actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fourcc = int(cap.get(cv2.CAP_PROP_FOURCC))
            fourcc_str = "".join([chr((fourcc >> 8 * i) & 0xFF) for i in range(4)])
            
            print(f"  ✅ {name}: {actual_width}x{actual_height}, format: {fourcc_str}")
            
            # Test multiple frames
            frame_count = 0
            start_time = time.time()
            for _ in range(30):  # Try 30 frames
                ret, frame = cap.read()
                if ret:
                    frame_count += 1
                time.sleep(0.01)
            
            elapsed = time.time() - start_time
            fps = frame_count / elapsed if elapsed > 0 else 0
            print(f"      Frame test: {frame_count}/30 frames, {fps:.1f} FPS")
            
            cap.release()
            return True
        else:
            print(f"  ❌ {name}: Cannot read frame")
    
    cap.release()
    return False

def test_all_cameras_simultaneously():
    """Test all 4 cameras simultaneously with best settings"""
    print(f"\n🎬 Testing all cameras simultaneously")
    
    camera_indices = [0, 2, 4, 6]
    cameras = {}
    
    # Initialize all cameras
    for cam_idx in camera_indices:
        # Use V4L2 backend with MJPG
        cap = cv2.VideoCapture(cam_idx, cv2.CAP_V4L2)
        if cap.isOpened():
            # Set MJPG format
            cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
            
            # Test frame read
            ret, frame = cap.read()
            if ret and frame is not None:
                cameras[cam_idx] = cap
                print(f"✅ Camera {cam_idx}: Initialized successfully")
            else:
                print(f"❌ Camera {cam_idx}: Cannot read initial frame")
                cap.release()
        else:
            print(f"❌ Camera {cam_idx}: Cannot open")
    
    if not cameras:
        print("❌ No cameras available for simultaneous test")
        return
    
    print(f"\n📊 Testing {len(cameras)} cameras for 5 seconds...")
    
    # Test simultaneous capture
    start_time = time.time()
    frame_counts = {cam_idx: 0 for cam_idx in cameras.keys()}
    
    try:
        while time.time() - start_time < 5:
            for cam_idx, cap in cameras.items():
                ret, frame = cap.read()
                if ret:
                    frame_counts[cam_idx] += 1
            time.sleep(0.01)
    except KeyboardInterrupt:
        print("\nTest interrupted")
    
    # Results
    elapsed = time.time() - start_time
    print(f"\n📈 Results after {elapsed:.1f} seconds:")
    for cam_idx, count in frame_counts.items():
        fps = count / elapsed
        print(f"  Camera {cam_idx}: {count} frames ({fps:.1f} FPS)")
    
    # Cleanup
    for cap in cameras.values():
        cap.release()

def main():
    print("🔍 Testing Cameras with MJPG Support")
    print("=" * 50)
    
    camera_indices = [0, 2, 4, 6]
    working_cameras = []
    
    # Test each camera individually
    for cam_idx in camera_indices:
        if test_camera_with_mjpg(cam_idx):
            working_cameras.append(cam_idx)
    
    print(f"\n📋 Summary:")
    print(f"Working cameras: {working_cameras}")
    print(f"Total working cameras: {len(working_cameras)}")
    
    if len(working_cameras) >= 2:
        # Test simultaneous capture
        test_all_cameras_simultaneously()
    
    print(f"\n✅ Testing completed!")
    if len(working_cameras) == 4:
        print("🎉 All 4 cameras are working! You can use: 0,2,4,6")
    elif len(working_cameras) >= 2:
        print(f"✅ {len(working_cameras)} cameras working: {working_cameras}")
    else:
        print("⚠️  Limited camera functionality detected")

if __name__ == "__main__":
    main()
