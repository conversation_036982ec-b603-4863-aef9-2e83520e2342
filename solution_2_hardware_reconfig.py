#!/usr/bin/env python3
"""
方案2: 硬件重新配置
通过重新分配USB端口来解决带宽限制
"""

import subprocess
import re

def analyze_usb_topology():
    """分析当前USB拓扑结构"""
    print("🔌 方案2: 硬件重新配置分析")
    print("=" * 50)
    
    print("📊 当前USB拓扑结构:")
    
    # 获取USB拓扑
    try:
        result = subprocess.run(['lsusb', '-t'], capture_output=True, text=True)
        topology = result.stdout
        print(topology)
        
        # 分析USB控制器
        print("\n🔍 USB控制器分析:")
        
        # 查找root hub
        root_hubs = re.findall(r'Bus (\d+)\.Port 1: Dev 1.*?(\d+)M', topology)
        for bus, speed in root_hubs:
            print(f"   Bus {bus}: {speed}M 带宽")
        
        # 查找摄像头设备
        print("\n📷 摄像头连接分析:")
        camera_lines = [line for line in topology.split('\n') if 'Video' in line]
        for line in camera_lines:
            print(f"   {line.strip()}")
            
    except Exception as e:
        print(f"❌ 无法获取USB拓扑: {e}")

def generate_hardware_solutions():
    """生成硬件解决方案"""
    print(f"\n🛠️ 硬件重新配置方案")
    print("=" * 50)
    
    solutions = [
        {
            "name": "方案2A: 分散到不同USB端口",
            "description": "将摄像头连接到不同的物理USB端口",
            "steps": [
                "1. 断开所有摄像头",
                "2. 查找主板上的不同USB控制器端口",
                "3. 将摄像头分别连接到:",
                "   - 摄像头0,2: 连接到USB控制器A",
                "   - 摄像头4,6: 连接到USB控制器B",
                "4. 重新测试带宽"
            ],
            "expected_result": "每个控制器承担2个摄像头，带宽充足",
            "difficulty": "中等",
            "cost": "免费"
        },
        {
            "name": "方案2B: 使用USB 3.0端口",
            "description": "将摄像头连接到USB 3.0端口获得更高带宽",
            "steps": [
                "1. 识别设备上的USB 3.0端口 (通常是蓝色)",
                "2. 使用USB 3.0 Hub扩展端口",
                "3. 将所有摄像头连接到USB 3.0 Hub",
                "4. 验证摄像头工作在USB 3.0模式"
            ],
            "expected_result": "USB 3.0提供5Gbps带宽，足够4个摄像头",
            "difficulty": "简单",
            "cost": "需要USB 3.0 Hub (~$20-50)"
        },
        {
            "name": "方案2C: 添加PCIe USB卡",
            "description": "安装额外的USB控制器卡",
            "steps": [
                "1. 购买PCIe USB 3.0扩展卡",
                "2. 安装到PCIe插槽",
                "3. 安装驱动程序",
                "4. 将部分摄像头连接到新控制器"
            ],
            "expected_result": "独立的USB控制器，完全解决带宽问题",
            "difficulty": "高",
            "cost": "PCIe USB卡 (~$30-100)"
        }
    ]
    
    for solution in solutions:
        print(f"\n📋 {solution['name']}")
        print(f"   描述: {solution['description']}")
        print(f"   难度: {solution['difficulty']}")
        print(f"   成本: {solution['cost']}")
        print(f"   预期结果: {solution['expected_result']}")
        print(f"   步骤:")
        for step in solution['steps']:
            print(f"     {step}")

def test_current_usb_ports():
    """测试当前可用的USB端口"""
    print(f"\n🧪 USB端口测试")
    print("=" * 50)
    
    print("📝 手动测试步骤:")
    print("""
1. 断开所有摄像头
2. 逐个连接摄像头到不同端口:
   
   测试序列:
   a) 只连接摄像头0到端口A -> 测试
   b) 只连接摄像头2到端口B -> 测试  
   c) 同时连接摄像头0(端口A) + 摄像头2(端口B) -> 测试
   d) 添加摄像头4到端口C -> 测试
   e) 添加摄像头6到端口D -> 测试

3. 使用以下命令测试每个配置:
   python test_cameras_mjpg.py

4. 记录哪些端口组合可以同时工作
    """)

def generate_detection_script():
    """生成USB端口检测脚本"""
    print(f"\n💻 USB端口检测脚本")
    print("=" * 50)
    
    script_content = '''#!/bin/bash
# USB端口检测脚本

echo "🔍 检测USB控制器和端口"
echo "=========================="

echo "📊 USB控制器列表:"
lspci | grep -i usb

echo -e "\\n📋 USB设备树:"
lsusb -t

echo -e "\\n🔌 USB设备详情:"
lsusb -v | grep -E "(Bus|iProduct|bMaxPower)" | head -20

echo -e "\\n💡 建议:"
echo "1. 查找不同的USB控制器"
echo "2. 将摄像头分散连接到不同控制器"
echo "3. 优先使用USB 3.0端口"
'''
    
    with open('detect_usb_ports.sh', 'w') as f:
        f.write(script_content)
    
    print("✅ 已生成 detect_usb_ports.sh")
    print("   运行: chmod +x detect_usb_ports.sh && ./detect_usb_ports.sh")

def main():
    print("🔌 USB带宽限制 - 硬件重新配置方案")
    print("=" * 60)
    
    # 分析当前拓扑
    analyze_usb_topology()
    
    # 生成解决方案
    generate_hardware_solutions()
    
    # 测试指导
    test_current_usb_ports()
    
    # 生成检测脚本
    generate_detection_script()
    
    print(f"\n✅ 硬件重新配置方案分析完成!")
    print(f"\n🎯 推荐行动:")
    print(f"   1. 先尝试方案2A (免费)")
    print(f"   2. 如果不行，考虑方案2B (购买USB 3.0 Hub)")
    print(f"   3. 最后选择方案2C (PCIe扩展卡)")

if __name__ == "__main__":
    main()
