#!/usr/bin/env python3
"""
Quick test for your specific camera configuration (0, 2, 4, 6)
"""

import cv2
import time
import numpy as np

def test_all_cameras():
    """Test all 4 cameras with different resolutions"""
    camera_indices = [0, 2, 4, 6]
    resolutions = [
        (640, 480),
        (1280, 720),
        (1920, 1080)
    ]
    
    print("🎥 Testing your cameras: 0, 2, 4, 6")
    print("=" * 50)
    
    for res_width, res_height in resolutions:
        print(f"\n📐 Testing resolution: {res_width}x{res_height}")
        
        cameras = {}
        for cam_idx in camera_indices:
            cap = cv2.VideoCapture(cam_idx)
            if cap.isOpened():
                # Set resolution
                cap.set(cv2.CAP_PROP_FRAME_WIDTH, res_width)
                cap.set(cv2.CAP_PROP_FRAME_HEIGHT, res_height)
                
                # Get actual resolution
                actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                
                # Test frame capture
                ret, frame = cap.read()
                if ret:
                    cameras[cam_idx] = cap
                    print(f"✅ Camera {cam_idx}: {actual_width}x{actual_height}")
                else:
                    print(f"❌ Camera {cam_idx}: Cannot read frame")
                    cap.release()
            else:
                print(f"❌ Camera {cam_idx}: Cannot open")
        
        # Test simultaneous capture
        if cameras:
            print(f"   Testing simultaneous capture from {len(cameras)} cameras...")
            start_time = time.time()
            frame_counts = {cam_idx: 0 for cam_idx in cameras.keys()}
            
            while time.time() - start_time < 2:  # 2 seconds test
                for cam_idx, cap in cameras.items():
                    ret, frame = cap.read()
                    if ret:
                        frame_counts[cam_idx] += 1
                time.sleep(0.01)
            
            for cam_idx, count in frame_counts.items():
                fps = count / 2
                print(f"   Camera {cam_idx}: {fps:.1f} FPS")
        
        # Cleanup
        for cap in cameras.values():
            cap.release()
        
        time.sleep(0.5)  # Brief pause between resolution tests

def test_multi_camera_display():
    """Test displaying all 4 cameras simultaneously"""
    camera_indices = [0, 2, 4, 6]
    print(f"\n🖥️  Testing multi-camera display (press 'q' to quit)")
    
    cameras = {}
    for cam_idx in camera_indices:
        cap = cv2.VideoCapture(cam_idx)
        if cap.isOpened():
            # Set to 640x480 for display test
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            cameras[cam_idx] = cap
            print(f"✅ Camera {cam_idx}: Ready for display")
        else:
            print(f"❌ Camera {cam_idx}: Cannot open")
    
    if not cameras:
        print("❌ No cameras available for display test")
        return
    
    print(f"📺 Displaying {len(cameras)} cameras. Press 'q' to quit.")
    
    try:
        while True:
            frames = {}
            for cam_idx, cap in cameras.items():
                ret, frame = cap.read()
                if ret:
                    # Resize for display
                    frame = cv2.resize(frame, (320, 240))
                    # Add camera label
                    cv2.putText(frame, f'Camera {cam_idx}', (10, 30), 
                               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                    frames[cam_idx] = frame
            
            if frames:
                # Create a 2x2 grid
                if len(frames) >= 4:
                    top_row = np.hstack([frames[0], frames[2]])
                    bottom_row = np.hstack([frames[4], frames[6]])
                    combined = np.vstack([top_row, bottom_row])
                elif len(frames) >= 2:
                    combined = np.hstack(list(frames.values()))
                else:
                    combined = list(frames.values())[0]
                
                cv2.imshow('Multi-Camera View (Press q to quit)', combined)
            
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
                
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    
    finally:
        for cap in cameras.values():
            cap.release()
        cv2.destroyAllWindows()

def main():
    print("🔍 Testing Your Camera Configuration")
    print("Detected cameras: 0, 2, 4, 6")
    print("=" * 50)
    
    # Test 1: Resolution capabilities
    test_all_cameras()
    
    # Test 2: Visual display test
    response = input("\n🎬 Do you want to test visual display? (y/n): ").lower()
    if response == 'y':
        test_multi_camera_display()
    
    print("\n✅ Camera testing completed!")
    print("\n📋 Summary:")
    print("- Your cameras are numbered: 0, 2, 4, 6")
    print("- Default configuration has been updated in the code")
    print("- Run: streamlit run ultralytics/solutions/streamlit_inference.py")
    print("- Select 'multi-webcam' and use camera indices: 0,2,4,6")

if __name__ == "__main__":
    main()
